"""
Authentication and token management for Jimeng API
"""

import random
import time
import hashlib
import uuid
from typing import List, Optional
from pydantic import BaseModel


class TokenManager:
    """Manages authentication tokens for Jimeng API"""
    
    def __init__(self, tokens: Optional[List[str]] = None):
        """
        Initialize token manager
        
        Args:
            tokens: List of session IDs for authentication
        """
        self.tokens = tokens or []
        self._device_id = random.randint(7000000000000000000, 7999999999999999999)
        self._web_id = random.randint(7000000000000000000, 7999999999999999999)
        self._user_id = str(uuid.uuid4()).replace('-', '')
    
    def add_token(self, token: str) -> None:
        """Add a new token to the pool"""
        if token not in self.tokens:
            self.tokens.append(token)
    
    def add_tokens(self, tokens: List[str]) -> None:
        """Add multiple tokens to the pool"""
        for token in tokens:
            self.add_token(token)
    
    def get_random_token(self) -> Optional[str]:
        """Get a random token from the pool"""
        if not self.tokens:
            return None
        return random.choice(self.tokens)
    
    def remove_token(self, token: str) -> None:
        """Remove a token from the pool"""
        if token in self.tokens:
            self.tokens.remove(token)
    
    def has_tokens(self) -> bool:
        """Check if there are any tokens available"""
        return len(self.tokens) > 0
    
    def generate_cookie(self, session_id: str) -> str:
        """Generate cookie string for API requests"""
        timestamp = int(time.time())
        cookie_parts = [
            f"_tea_web_id={self._web_id}",
            "is_staff_user=false",
            "store-region=cn-gd",
            "store-region-src=uid",
            f"sid_guard={session_id}%7C{timestamp}%7C5184000%7CMon%2C+03-Feb-2025+08%3A17%3A09+GMT",
            f"uid_tt={self._user_id}",
            f"uid_tt_ss={self._user_id}",
            f"sid_tt={session_id}",
            f"sessionid={session_id}",
            f"sessionid_ss={session_id}",
        ]
        return "; ".join(cookie_parts)
    
    def generate_sign(self, uri: str, platform_code: str, version_code: str, device_time: int) -> str:
        """Generate signature for API requests"""
        # Extract last 7 characters from URI
        uri_suffix = uri[-7:] if len(uri) >= 7 else uri
        
        # Create signature string
        sign_string = f"9e2c|{uri_suffix}|{platform_code}|{version_code}|{device_time}||11ac"
        
        # Generate MD5 hash
        return hashlib.md5(sign_string.encode()).hexdigest()
    
    def get_fake_headers(self, session_id: str, uri: str, platform_code: str = "7", 
                        version_code: str = "5.8.0", assistant_id: str = "513695") -> dict:
        """Generate fake headers for API requests"""
        device_time = int(time.time())
        sign = self.generate_sign(uri, platform_code, version_code, device_time)
        
        return {
            "Accept": "application/json, text/plain, */*",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Cache-Control": "no-cache",
            "Last-Event-Id": "undefined",
            "Appid": assistant_id,
            "Appvr": version_code,
            "Origin": "https://jimeng.jianying.com",
            "Pragma": "no-cache",
            "Priority": "u=1, i",
            "Referer": "https://jimeng.jianying.com",
            "Pf": platform_code,
            "Sec-Ch-Ua": '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
            "Sec-Ch-Ua-Mobile": "?0",
            "Sec-Ch-Ua-Platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Cookie": self.generate_cookie(session_id),
            "Device-Time": str(device_time),
            "Sign": sign,
            "Sign-Ver": "1",
        }
    
    @property
    def device_id(self) -> int:
        """Get device ID"""
        return self._device_id
    
    @property
    def web_id(self) -> int:
        """Get web ID"""
        return self._web_id
    
    @property
    def user_id(self) -> str:
        """Get user ID"""
        return self._user_id


def parse_tokens_from_auth_header(auth_header: str) -> List[str]:
    """
    Parse tokens from Authorization header
    
    Args:
        auth_header: Authorization header value (e.g., "Bearer token1,token2,token3")
    
    Returns:
        List of tokens
    """
    if not auth_header:
        return []
    
    # Remove "Bearer " prefix if present
    if auth_header.startswith("Bearer "):
        auth_header = auth_header[7:]
    
    # Split by comma and strip whitespace
    tokens = [token.strip() for token in auth_header.split(",") if token.strip()]
    
    return tokens
