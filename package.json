{"name": "jimeng-free-api", "version": "0.0.6", "description": "jimeng Free API Server", "type": "module", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "directories": {"dist": "dist"}, "files": ["dist/"], "scripts": {"dev": "tsup src/index.ts --format cjs,esm --sourcemap --dts --publicDir public --watch --onSuccess \"node --enable-source-maps --no-node-snapshot dist/index.js\"", "start": "node --enable-source-maps --no-node-snapshot dist/index.js", "build": "tsup src/index.ts --format cjs,esm --sourcemap --dts --clean --publicDir public"}, "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"axios": "^1.6.7", "colors": "^1.4.0", "crc-32": "^1.2.2", "cron": "^3.1.6", "date-fns": "^3.3.1", "eventsource-parser": "^1.1.2", "form-data": "^4.0.0", "fs-extra": "^11.2.0", "koa": "^2.15.0", "koa-body": "^5.0.0", "koa-bodyparser": "^4.4.1", "koa-range": "^0.3.0", "koa-router": "^12.0.1", "koa2-cors": "^2.0.6", "lodash": "^4.17.21", "mime": "^4.0.1", "minimist": "^1.2.8", "randomstring": "^1.3.0", "uuid": "^9.0.1", "yaml": "^2.3.4"}, "devDependencies": {"@types/lodash": "^4.14.202", "@types/mime": "^3.0.4", "tsup": "^8.0.2", "typescript": "^5.3.3"}}