# Jimeng MCP Server

一个基于 Model Context Protocol (MCP) 的服务器，提供对即梦AI图像生成和对话补全功能的访问。

## 功能特性

- 🎨 **图像生成**: 支持多种模型的AI图像生成
- 💬 **对话补全**: 兼容OpenAI格式的聊天接口
- 🎯 **积分管理**: 自动获取和管理即梦积分
- 🔐 **多Token支持**: 支持多个session token轮询使用
- 📡 **MCP协议**: 标准的Model Context Protocol接口

## 支持的模型

- `jimeng-3.0` (默认)
- `jimeng-2.1`
- `jimeng-2.0-pro`
- `jimeng-2.0`
- `jimeng-1.4`
- `jimeng-xl-pro`

## 安装

### 使用 uv (推荐)

```bash
# 克隆项目
git clone <repository-url>
cd jimeng-mcp-server

# 安装依赖
uv sync

# 运行服务器
uv run jimeng-mcp-server --tokens "your_session_id1,your_session_id2"
```

### 使用 pip

```bash
# 克隆项目
git clone <repository-url>
cd jimeng-mcp-server

# 安装依赖
pip install -e .

# 运行服务器
jimeng-mcp-server --tokens "your_session_id1,your_session_id2"
```

## 获取Session ID

1. 访问 [即梦官网](https://jimeng.jianying.com/)
2. 登录你的账号
3. 按F12打开开发者工具
4. 在 Application > Cookies 中找到 `sessionid` 的值
5. 这个值就是你的认证token

## 使用方法

### 命令行参数

```bash
jimeng-mcp-server [OPTIONS]

Options:
  --tokens TEXT          逗号分隔的session token列表
  --token-file TEXT      包含token的文件路径（每行一个）
  --log-level LEVEL      日志级别 (DEBUG, INFO, WARNING, ERROR)
  --help                 显示帮助信息
```

### Token文件格式

创建一个文本文件，每行一个session token：

```
session_token_1
session_token_2
session_token_3
```

然后使用 `--token-file` 参数：

```bash
jimeng-mcp-server --token-file tokens.txt
```

## MCP工具

### 1. jimeng_generate_images

生成图像的工具。

**参数:**
- `model` (string): 使用的模型名称
- `prompt` (string): 图像生成提示词
- `negative_prompt` (string, 可选): 负面提示词
- `width` (integer, 可选): 图像宽度 (256-2048)
- `height` (integer, 可选): 图像高度 (256-2048)
- `sample_strength` (number, 可选): 采样强度 (0.0-1.0)
- `session_id` (string, 可选): 特定的session ID

**示例:**
```json
{
  "model": "jimeng-3.0",
  "prompt": "一只可爱的熊猫在竹林中",
  "width": 1024,
  "height": 1024,
  "sample_strength": 0.7
}
```

### 2. jimeng_chat_completion

聊天补全工具，将文本转换为图像。

**参数:**
- `model` (string): 使用的模型（可包含尺寸，如 "jimeng-3.0:1024x768"）
- `messages` (array): 聊天消息列表
- `stream` (boolean, 可选): 是否流式响应
- `session_id` (string, 可选): 特定的session ID

**示例:**
```json
{
  "model": "jimeng-3.0:1024x768",
  "messages": [
    {
      "role": "user",
      "content": "画一幅山水画"
    }
  ]
}
```

### 3. jimeng_get_credit

获取当前积分信息。

**参数:**
- `session_id` (string, 可选): 特定的session ID

### 4. jimeng_receive_credit

领取每日积分（通常为66积分）。

**参数:**
- `session_id` (string, 可选): 特定的session ID

### 5. jimeng_check_token

检查session token是否有效。

**参数:**
- `session_id` (string): 要检查的session ID

## 配置

可以通过环境变量配置服务器：

```bash
export JIMENG_BASE_URL="https://jimeng.jianying.com"
export JIMENG_TIMEOUT="15"
export JIMENG_MAX_RETRY_COUNT="3"
export JIMENG_DEFAULT_MODEL="jimeng-3.0"
```

## 在MCP客户端中使用

### Claude Desktop配置

在Claude Desktop的配置文件中添加：

```json
{
  "mcpServers": {
    "jimeng": {
      "command": "uv",
      "args": ["run", "jimeng-mcp-server", "--tokens", "your_session_id"],
      "cwd": "/path/to/jimeng-mcp-server"
    }
  }
}
```

### 其他MCP客户端

参考各客户端的文档，配置MCP服务器连接。

## 开发

### 安装开发依赖

```bash
uv sync --dev
```

### 运行测试

```bash
uv run pytest
```

### 代码格式化

```bash
uv run black src/
uv run isort src/
```

### 类型检查

```bash
uv run mypy src/
```

## 注意事项

1. **免责声明**: 这是一个逆向工程项目，仅供学习研究使用
2. **使用限制**: 请遵守即梦官方的使用条款，避免过度使用
3. **积分消耗**: 每次图像生成会消耗积分，请合理使用
4. **Token安全**: 请妥善保管你的session token，不要泄露给他人

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！

## 相关项目

- [jimeng-free-api](https://github.com/LLM-Red-Team/jimeng-free-api) - 原始的即梦免费API项目
- [Model Context Protocol](https://modelcontextprotocol.io/) - MCP协议官方文档
