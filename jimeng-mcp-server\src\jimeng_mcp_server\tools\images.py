"""
Image generation tool for Jimeng MCP Server
"""

import uuid
from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field

from ..config import config
from ..core.client import <PERSON>eng<PERSON><PERSON>, JimengAPIException


class ImageGenerationRequest(BaseModel):
    """Request model for image generation"""
    model: str = Field(default=config.default_model, description="Model to use for generation")
    prompt: str = Field(description="Text prompt for image generation")
    negative_prompt: str = Field(default="", description="Negative prompt to avoid certain elements")
    width: int = Field(default=config.default_width, description="Image width")
    height: int = Field(default=config.default_height, description="Image height")
    sample_strength: float = Field(default=config.default_sample_strength, description="Sample strength (0.0-1.0)")


class ImageGenerationResponse(BaseModel):
    """Response model for image generation"""
    created: int
    data: List[Dict[str, str]]


class ImageTool:
    """MCP tool for image generation"""
    
    # Model mapping from the original API
    MODEL_MAP = {
        "jimeng-3.0": "high_aes_general_v30l:general_v3.0_18b",
        "jimeng-2.1": "high_aes_general_v21_L:general_v2.1_L",
        "jimeng-2.0-pro": "high_aes_general_v20_L:general_v2.0_L",
        "jimeng-2.0": "high_aes_general_v20:general_v2.0",
        "jimeng-1.4": "high_aes_general_v14:general_v1.4",
        "jimeng-xl-pro": "text2img_xl_sft",
    }
    
    def __init__(self, client: JimengClient):
        """
        Initialize image tool
        
        Args:
            client: Jimeng API client
        """
        self.client = client
    
    def get_model_mapping(self, model: str) -> str:
        """Get internal model mapping"""
        return self.MODEL_MAP.get(model, self.MODEL_MAP[config.default_model])
    
    async def generate_images(
        self,
        model: str,
        prompt: str,
        width: int = config.default_width,
        height: int = config.default_height,
        sample_strength: float = config.default_sample_strength,
        negative_prompt: str = "",
        session_id: Optional[str] = None
    ) -> List[str]:
        """
        Generate images using Jimeng API

        Args:
            model: Model name
            prompt: Text prompt
            width: Image width
            height: Image height
            sample_strength: Sample strength
            negative_prompt: Negative prompt
            session_id: Session ID for authentication

        Returns:
            List of image URLs
        """
        import random
        import json
        import asyncio

        # Get credit info and receive daily credit if needed
        credit_info = await self.client.get_credit(session_id)
        if credit_info.total_credit <= 0:
            await self.client.receive_credit(session_id)

        # Get internal model mapping
        internal_model = self.get_model_mapping(model)

        # Generate component ID and other UUIDs
        component_id = str(uuid.uuid4())
        submit_id = str(uuid.uuid4())
        draft_id = str(uuid.uuid4())
        ability_id = str(uuid.uuid4())
        generate_id = str(uuid.uuid4())
        core_param_id = str(uuid.uuid4())
        large_image_info_id = str(uuid.uuid4())
        history_option_id = str(uuid.uuid4())

        # Prepare babi_param
        babi_param = {
            "scenario": "image_video_generation",
            "feature_key": "aigc_to_image",
            "feature_entrance": "to_image",
            "feature_entrance_detail": f"to_image-{internal_model}"
        }

        # Prepare draft content
        draft_content = {
            "type": "draft",
            "id": draft_id,
            "min_version": "3.0.2",
            "is_from_tsn": True,
            "version": "3.0.2",
            "main_component_id": component_id,
            "component_list": [
                {
                    "type": "image_base_component",
                    "id": component_id,
                    "min_version": "3.0.2",
                    "generate_type": "generate",
                    "aigc_mode": "workbench",
                    "abilities": {
                        "type": "",
                        "id": ability_id,
                        "generate": {
                            "type": "",
                            "id": generate_id,
                            "core_param": {
                                "type": "",
                                "id": core_param_id,
                                "model": internal_model,
                                "prompt": prompt,
                                "negative_prompt": negative_prompt,
                                "seed": random.randint(2500000000, 2600000000),
                                "sample_strength": sample_strength,
                                "image_ratio": 1,
                                "large_image_info": {
                                    "type": "",
                                    "id": large_image_info_id,
                                    "height": height,
                                    "width": width
                                }
                            },
                            "history_option": {
                                "type": "",
                                "id": history_option_id
                            }
                        }
                    }
                }
            ]
        }

        # Prepare metrics_extra
        metrics_extra = {
            "templateId": "",
            "generateCount": 1,
            "promptSource": "custom",
            "templateSource": "",
            "lastRequestId": "",
            "originRequestId": ""
        }

        # Prepare request data
        request_data = {
            "extend": {
                "root_model": internal_model,
                "template_id": ""
            },
            "submit_id": submit_id,
            "metrics_extra": json.dumps(metrics_extra),
            "draft_content": json.dumps(draft_content),
            "http_common_info": {
                "aid": int(config.default_assistant_id)
            }
        }

        # Make initial generation request
        response_data = await self.client._request(
            method="POST",
            uri="/mweb/v1/aigc_draft/generate",
            session_id=session_id,
            params={
                "babi_param": json.dumps(babi_param)
            },
            data=request_data,
            headers={
                "Referer": "https://jimeng.jianying.com/ai-tool/image/generate"
            }
        )

        # Get history ID
        history_id = response_data.get("history_record_id")
        if not history_id:
            raise JimengAPIException("History record ID not found", error_code="NO_HISTORY_ID")

        # Poll for completion
        status = 20  # 20 = processing, 10 = completed, 30 = failed
        item_list = []

        while status == 20:
            await asyncio.sleep(1)  # Wait 1 second before polling

            # Check generation status
            status_data = await self.client._request(
                method="POST",
                uri="/mweb/v1/get_history_by_ids",
                session_id=session_id,
                data={
                    "history_ids": [history_id],
                    "image_info": {
                        "width": 2048,
                        "height": 2048,
                        "format": "webp",
                        "image_scene_list": [
                            {
                                "scene": "normal",
                                "width": 1080,
                                "height": 1080,
                                "uniq_key": "1080",
                                "format": "webp"
                            }
                        ]
                    },
                    "http_common_info": {
                        "aid": int(config.default_assistant_id)
                    }
                }
            )

            if history_id not in status_data:
                raise JimengAPIException("History record not found", error_code="HISTORY_NOT_FOUND")

            record = status_data[history_id]
            status = record.get("status", 30)
            fail_code = record.get("fail_code")
            item_list = record.get("item_list", [])

        # Check final status
        if status == 30:  # Failed
            if fail_code == "2038":
                raise JimengAPIException("Content filtered", error_code="CONTENT_FILTERED")
            else:
                raise JimengAPIException("Image generation failed", error_code="GENERATION_FAILED")

        # Extract image URLs
        image_urls = []
        for item in item_list:
            image_url = None

            # Try to get large image URL first
            if (item.get("image") and
                item["image"].get("large_images") and
                len(item["image"]["large_images"]) > 0 and
                item["image"]["large_images"][0].get("image_url")):
                image_url = item["image"]["large_images"][0]["image_url"]

            # Fallback to cover URL
            elif (item.get("common_attr") and
                  item["common_attr"].get("cover_url")):
                image_url = item["common_attr"]["cover_url"]

            if image_url:
                image_urls.append(image_url)

        if not image_urls:
            raise JimengAPIException("No image URLs found in response", error_code="NO_IMAGE_URLS")

        return image_urls
    
    @staticmethod
    def get_tool_definition() -> Dict[str, Any]:
        """Get MCP tool definition for image generation"""
        return {
            "name": "jimeng_generate_images",
            "description": "Generate images using Jimeng AI. Supports multiple models and customizable parameters.",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "model": {
                        "type": "string",
                        "description": "Model to use for generation",
                        "enum": config.available_models,
                        "default": config.default_model
                    },
                    "prompt": {
                        "type": "string",
                        "description": "Text prompt for image generation"
                    },
                    "negative_prompt": {
                        "type": "string",
                        "description": "Negative prompt to avoid certain elements",
                        "default": ""
                    },
                    "width": {
                        "type": "integer",
                        "description": "Image width in pixels",
                        "minimum": 256,
                        "maximum": 2048,
                        "default": config.default_width
                    },
                    "height": {
                        "type": "integer", 
                        "description": "Image height in pixels",
                        "minimum": 256,
                        "maximum": 2048,
                        "default": config.default_height
                    },
                    "sample_strength": {
                        "type": "number",
                        "description": "Sample strength (0.0-1.0)",
                        "minimum": 0.0,
                        "maximum": 1.0,
                        "default": config.default_sample_strength
                    },
                    "session_id": {
                        "type": "string",
                        "description": "Session ID for authentication (optional if tokens are configured)"
                    }
                },
                "required": ["prompt"]
            }
        }
    
    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute image generation tool
        
        Args:
            arguments: Tool arguments
        
        Returns:
            Tool execution result
        """
        try:
            # Validate and extract arguments
            request = ImageGenerationRequest(**arguments)
            
            # Generate images
            image_urls = await self.generate_images(
                model=request.model,
                prompt=request.prompt,
                width=request.width,
                height=request.height,
                sample_strength=request.sample_strength,
                negative_prompt=request.negative_prompt,
                session_id=arguments.get("session_id")
            )
            
            # Format response
            import time
            response_data = [{"url": url} for url in image_urls]
            
            return {
                "created": int(time.time()),
                "data": response_data,
                "model": request.model,
                "prompt": request.prompt
            }
            
        except Exception as e:
            return {
                "error": str(e),
                "error_type": type(e).__name__
            }
