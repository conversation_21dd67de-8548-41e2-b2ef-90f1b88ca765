"""
HTTP client for Jimeng API
"""

import asyncio
import json
import uuid
from typing import Any, Dict, List, Optional, Union
import httpx
from pydantic import BaseModel

from ..config import config
from .auth import TokenManager


class JimengAPIException(Exception):
    """Exception raised when Jimeng API returns an error"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, response_data: Optional[Dict] = None):
        super().__init__(message)
        self.error_code = error_code
        self.response_data = response_data


class CreditInfo(BaseModel):
    """Credit information model"""
    gift_credit: int
    purchase_credit: int
    vip_credit: int
    total_credit: int


class JimengClient:
    """HTTP client for Jimeng API"""
    
    def __init__(self, token_manager: Optional[TokenManager] = None):
        """
        Initialize Jimeng client
        
        Args:
            token_manager: Token manager instance
        """
        self.token_manager = token_manager or TokenManager()
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(config.timeout),
            limits=httpx.Limits(max_keepalive_connections=20, max_connections=100)
        )
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()
    
    def _check_result(self, response_data: Dict[str, Any]) -> Any:
        """Check API response and raise exception if error"""
        ret = response_data.get("ret")
        errmsg = response_data.get("errmsg", "Unknown error")
        data = response_data.get("data")
        
        # If ret is not a number, return the whole response
        if not isinstance(ret, (int, str)) or not str(ret).isdigit():
            return response_data
        
        ret = str(ret)
        
        if ret == "0":
            return data
        elif ret == "5000":
            raise JimengAPIException(
                f"[无法生成图像]: 即梦积分可能不足，{errmsg}",
                error_code="INSUFFICIENT_POINTS",
                response_data=response_data
            )
        else:
            raise JimengAPIException(
                f"[请求jimeng失败]: {errmsg}",
                error_code="API_REQUEST_FAILED", 
                response_data=response_data
            )
    
    async def _request(
        self,
        method: str,
        uri: str,
        session_id: Optional[str] = None,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Make HTTP request to Jimeng API
        
        Args:
            method: HTTP method
            uri: API endpoint URI
            session_id: Session ID for authentication
            params: Query parameters
            data: Request body data
            headers: Additional headers
            **kwargs: Additional httpx request arguments
        
        Returns:
            Response data
        """
        if not session_id:
            session_id = self.token_manager.get_random_token()
            if not session_id:
                raise JimengAPIException("No authentication tokens available")
        
        # Build URL
        url = f"{config.base_url}{uri}"
        
        # Build default parameters
        default_params = {
            "aid": config.default_assistant_id,
            "device_platform": "web",
            "region": "CN",
            "web_id": self.token_manager.web_id,
        }
        if params:
            default_params.update(params)
        
        # Build headers
        request_headers = self.token_manager.get_fake_headers(
            session_id=session_id,
            uri=uri,
            platform_code=config.platform_code,
            version_code=config.version_code,
            assistant_id=config.default_assistant_id
        )
        if headers:
            request_headers.update(headers)
        
        # Make request
        response = await self.client.request(
            method=method,
            url=url,
            params=default_params,
            json=data,
            headers=request_headers,
            **kwargs
        )
        
        # Handle response
        if response.status_code >= 400:
            raise JimengAPIException(
                f"HTTP {response.status_code}: {response.text}",
                error_code="HTTP_ERROR"
            )
        
        try:
            response_data = response.json()
        except json.JSONDecodeError:
            raise JimengAPIException(
                f"Invalid JSON response: {response.text}",
                error_code="INVALID_JSON"
            )
        
        return self._check_result(response_data)
    
    async def get_credit(self, session_id: Optional[str] = None) -> CreditInfo:
        """
        Get credit information
        
        Args:
            session_id: Session ID for authentication
        
        Returns:
            Credit information
        """
        data = await self._request(
            method="POST",
            uri="/commerce/v1/benefits/user_credit",
            session_id=session_id,
            data={},
            headers={"Referer": "https://jimeng.jianying.com/ai-tool/image/generate"}
        )
        
        credit = data.get("credit", {})
        gift_credit = credit.get("gift_credit", 0)
        purchase_credit = credit.get("purchase_credit", 0)
        vip_credit = credit.get("vip_credit", 0)
        
        return CreditInfo(
            gift_credit=gift_credit,
            purchase_credit=purchase_credit,
            vip_credit=vip_credit,
            total_credit=gift_credit + purchase_credit + vip_credit
        )
    
    async def receive_credit(self, session_id: Optional[str] = None) -> int:
        """
        Receive daily credit
        
        Args:
            session_id: Session ID for authentication
        
        Returns:
            Current total credits after receiving
        """
        data = await self._request(
            method="POST",
            uri="/commerce/v1/benefits/credit_receive",
            session_id=session_id,
            data={"time_zone": "Asia/Shanghai"},
            headers={"Referer": "https://jimeng.jianying.com/ai-tool/image/generate"}
        )
        
        return data.get("cur_total_credits", 0)
    
    async def check_token_status(self, session_id: str) -> bool:
        """
        Check if a token is valid
        
        Args:
            session_id: Session ID to check
        
        Returns:
            True if token is valid, False otherwise
        """
        try:
            await self._request(
                method="POST",
                uri="/passport/account/info/v2",
                session_id=session_id,
                params={"account_sdk_source": "web"}
            )
            return True
        except JimengAPIException:
            return False
