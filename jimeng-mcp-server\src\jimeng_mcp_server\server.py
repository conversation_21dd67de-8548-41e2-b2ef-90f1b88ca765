"""
Jimeng MCP Server

A Model Context Protocol (MCP) server that provides access to Jimeng AI's
image generation and chat completion capabilities.
"""

import asyncio
import json
import logging
import sys
from typing import Any, Dict, List, Optional, Sequence
import argparse

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    CallToolRequest,
    CallToolResult,
    ListToolsRequest,
    ListToolsResult,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
)

from .config import config
from .core import JimengClient, TokenManager
from .core.auth import parse_tokens_from_auth_header
from .tools import ChatTool, ImageTool, CreditTool


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class JimengMCPServer:
    """Jimeng MCP Server implementation"""
    
    def __init__(self, tokens: Optional[List[str]] = None):
        """
        Initialize Jimeng MCP Server
        
        Args:
            tokens: List of session tokens for authentication
        """
        self.server = Server("jimeng-mcp-server")
        self.token_manager = TokenManager(tokens or [])
        self.client: Optional[JimengClient] = None
        self.chat_tool: Optional[ChatTool] = None
        self.image_tool: Optional[ImageTool] = None
        self.credit_tool: Optional[CreditTool] = None
        
        # Register handlers
        self._register_handlers()
    
    def _register_handlers(self):
        """Register MCP protocol handlers"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> ListToolsResult:
            """List available tools"""
            tools = [
                Tool(
                    name="jimeng_chat_completion",
                    description="Create chat completion using Jimeng AI. Generates images based on text prompts in chat format.",
                    inputSchema=ChatTool.get_tool_definition()["inputSchema"]
                ),
                Tool(
                    name="jimeng_generate_images", 
                    description="Generate images using Jimeng AI. Supports multiple models and customizable parameters.",
                    inputSchema=ImageTool.get_tool_definition()["inputSchema"]
                ),
                Tool(
                    name="jimeng_get_credit",
                    description="Get current credit information including gift, purchase, and VIP credits.",
                    inputSchema=CreditTool.get_credit_info_tool_definition()["inputSchema"]
                ),
                Tool(
                    name="jimeng_receive_credit",
                    description="Receive daily credit allowance. Usually provides 66 credits per day.",
                    inputSchema=CreditTool.get_receive_credit_tool_definition()["inputSchema"]
                ),
                Tool(
                    name="jimeng_check_token",
                    description="Check if a session token is valid and active.",
                    inputSchema=CreditTool.get_token_status_tool_definition()["inputSchema"]
                )
            ]
            
            return ListToolsResult(tools=tools)
        
        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> CallToolResult:
            """Handle tool calls"""
            try:
                # Initialize client if not already done
                if self.client is None:
                    await self._initialize_client()
                
                # Handle token from arguments or use configured tokens
                session_id = arguments.get("session_id")
                if session_id and session_id not in self.token_manager.tokens:
                    self.token_manager.add_token(session_id)
                
                # Route to appropriate tool
                if name == "jimeng_chat_completion":
                    result = await self.chat_tool.execute(arguments)
                elif name == "jimeng_generate_images":
                    result = await self.image_tool.execute(arguments)
                elif name == "jimeng_get_credit":
                    result = await self.credit_tool.execute_get_credit(arguments)
                elif name == "jimeng_receive_credit":
                    result = await self.credit_tool.execute_receive_credit(arguments)
                elif name == "jimeng_check_token":
                    result = await self.credit_tool.execute_check_token(arguments)
                else:
                    raise ValueError(f"Unknown tool: {name}")
                
                # Format result as MCP content
                if "error" in result:
                    content = [TextContent(
                        type="text",
                        text=f"Error: {result['error']}"
                    )]
                else:
                    # Convert result to JSON string for text content
                    content = [TextContent(
                        type="text", 
                        text=json.dumps(result, indent=2, ensure_ascii=False)
                    )]
                
                return CallToolResult(content=content)
                
            except Exception as e:
                logger.error(f"Error calling tool {name}: {e}")
                return CallToolResult(
                    content=[TextContent(
                        type="text",
                        text=f"Error executing tool {name}: {str(e)}"
                    )],
                    isError=True
                )
    
    async def _initialize_client(self):
        """Initialize the Jimeng client and tools"""
        if self.client is None:
            self.client = JimengClient(self.token_manager)
            self.chat_tool = ChatTool(self.client)
            self.image_tool = ImageTool(self.client)
            self.credit_tool = CreditTool(self.client)
    
    async def run(self):
        """Run the MCP server"""
        logger.info("Starting Jimeng MCP Server...")
        logger.info(f"Available models: {', '.join(config.available_models)}")
        logger.info(f"Default model: {config.default_model}")
        
        if self.token_manager.has_tokens():
            logger.info(f"Configured with {len(self.token_manager.tokens)} authentication tokens")
        else:
            logger.warning("No authentication tokens configured. Tokens must be provided in tool calls.")
        
        # Initialize client
        await self._initialize_client()
        
        # Run server
        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="jimeng-mcp-server",
                    server_version="0.1.0",
                    capabilities=self.server.get_capabilities(
                        notification_options=None,
                        experimental_capabilities=None,
                    ),
                ),
            )
    
    async def close(self):
        """Close the server and cleanup resources"""
        if self.client:
            await self.client.close()


def parse_args() -> argparse.Namespace:
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Jimeng MCP Server - Provides access to Jimeng AI via MCP protocol"
    )
    
    parser.add_argument(
        "--tokens",
        type=str,
        help="Comma-separated list of session tokens for authentication"
    )
    
    parser.add_argument(
        "--token-file",
        type=str,
        help="Path to file containing session tokens (one per line)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Set logging level"
    )
    
    return parser.parse_args()


def load_tokens_from_file(file_path: str) -> List[str]:
    """Load tokens from file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            tokens = [line.strip() for line in f if line.strip()]
        return tokens
    except FileNotFoundError:
        logger.error(f"Token file not found: {file_path}")
        return []
    except Exception as e:
        logger.error(f"Error reading token file: {e}")
        return []


async def main():
    """Main entry point"""
    args = parse_args()
    
    # Set logging level
    logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    # Load tokens
    tokens = []
    
    if args.tokens:
        tokens.extend(parse_tokens_from_auth_header(f"Bearer {args.tokens}"))
    
    if args.token_file:
        file_tokens = load_tokens_from_file(args.token_file)
        tokens.extend(file_tokens)
    
    # Create and run server
    server = JimengMCPServer(tokens)
    
    try:
        await server.run()
    except KeyboardInterrupt:
        logger.info("Server interrupted by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
        sys.exit(1)
    finally:
        await server.close()


if __name__ == "__main__":
    asyncio.run(main())
