"""
Chat completion tool for Jimeng MCP Server
"""

import time
import uuid
from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field

from ..config import config
from ..core.client import <PERSON>eng<PERSON><PERSON>, JimengAPIException
from .images import ImageTool


class ChatMessage(BaseModel):
    """Chat message model"""
    role: str = Field(description="Message role (user, assistant, system)")
    content: str = Field(description="Message content")


class ChatCompletionRequest(BaseModel):
    """Request model for chat completion"""
    model: str = Field(default=config.default_model, description="Model to use")
    messages: List[ChatMessage] = Field(description="List of chat messages")
    stream: bool = Field(default=False, description="Whether to stream the response")


class ChatTool:
    """MCP tool for chat completion (image generation via text)"""
    
    def __init__(self, client: JimengClient):
        """
        Initialize chat tool
        
        Args:
            client: Jimeng API client
        """
        self.client = client
        self.image_tool = ImageTool(client)
    
    def parse_model(self, model: str) -> Dict[str, Any]:
        """
        Parse model string to extract model name and dimensions
        
        Args:
            model: Model string (e.g., "jimeng-3.0:1024x768")
        
        Returns:
            Dictionary with model, width, height
        """
        if ":" in model:
            model_name, size = model.split(":", 1)
            # Try to extract width and height from size string
            if "x" in size:
                try:
                    width_str, height_str = size.split("x", 1)
                    width = max(256, min(2048, int(width_str)))
                    height = max(256, min(2048, int(height_str)))
                    # Ensure even numbers
                    width = (width // 2) * 2
                    height = (height // 2) * 2
                except ValueError:
                    width = config.default_width
                    height = config.default_height
            else:
                width = config.default_width
                height = config.default_height
        else:
            model_name = model
            width = config.default_width
            height = config.default_height
        
        return {
            "model": model_name,
            "width": width,
            "height": height
        }
    
    async def create_completion(
        self,
        messages: List[ChatMessage],
        model: str = config.default_model,
        stream: bool = False,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create chat completion (generate images based on text)
        
        Args:
            messages: List of chat messages
            model: Model to use
            stream: Whether to stream response
            session_id: Session ID for authentication
        
        Returns:
            Chat completion response
        """
        if not messages:
            raise JimengAPIException("Messages cannot be empty", error_code="EMPTY_MESSAGES")
        
        # Parse model to get dimensions
        model_info = self.parse_model(model)
        
        # Get the last user message as the prompt
        user_messages = [msg for msg in messages if msg.role == "user"]
        if not user_messages:
            raise JimengAPIException("No user messages found", error_code="NO_USER_MESSAGES")
        
        prompt = user_messages[-1].content
        
        # Generate images using the prompt
        image_urls = await self.image_tool.generate_images(
            model=model_info["model"],
            prompt=prompt,
            width=model_info["width"],
            height=model_info["height"],
            session_id=session_id
        )
        
        # Format images as markdown
        image_content = "\n".join([
            f"![image_{i}]({url})" for i, url in enumerate(image_urls)
        ])
        
        # Create response
        response = {
            "id": str(uuid.uuid4()),
            "model": model,
            "object": "chat.completion",
            "choices": [
                {
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": image_content
                    },
                    "finish_reason": "stop"
                }
            ],
            "usage": {
                "prompt_tokens": len(prompt.split()),
                "completion_tokens": len(image_urls),
                "total_tokens": len(prompt.split()) + len(image_urls)
            },
            "created": int(time.time())
        }
        
        return response
    
    @staticmethod
    def get_tool_definition() -> Dict[str, Any]:
        """Get MCP tool definition for chat completion"""
        return {
            "name": "jimeng_chat_completion",
            "description": "Create chat completion using Jimeng AI. Generates images based on text prompts in chat format.",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "model": {
                        "type": "string",
                        "description": "Model to use (can include dimensions like 'jimeng-3.0:1024x768')",
                        "default": config.default_model
                    },
                    "messages": {
                        "type": "array",
                        "description": "List of chat messages",
                        "items": {
                            "type": "object",
                            "properties": {
                                "role": {
                                    "type": "string",
                                    "enum": ["user", "assistant", "system"],
                                    "description": "Message role"
                                },
                                "content": {
                                    "type": "string",
                                    "description": "Message content"
                                }
                            },
                            "required": ["role", "content"]
                        }
                    },
                    "stream": {
                        "type": "boolean",
                        "description": "Whether to stream the response",
                        "default": False
                    },
                    "session_id": {
                        "type": "string",
                        "description": "Session ID for authentication (optional if tokens are configured)"
                    }
                },
                "required": ["messages"]
            }
        }
    
    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute chat completion tool
        
        Args:
            arguments: Tool arguments
        
        Returns:
            Tool execution result
        """
        try:
            # Validate and extract arguments
            request = ChatCompletionRequest(**arguments)
            
            # Create completion
            response = await self.create_completion(
                messages=request.messages,
                model=request.model,
                stream=request.stream,
                session_id=arguments.get("session_id")
            )
            
            return response
            
        except Exception as e:
            return {
                "error": str(e),
                "error_type": type(e).__name__
            }
