"""
Configuration module for Jimeng MCP Server
"""

import os
from typing import List, Optional
from pydantic import BaseModel, Field


class JimengConfig(BaseModel):
    """Configuration for Jimeng MCP Server"""
    
    # API Configuration
    base_url: str = Field(
        default="https://jimeng.jianying.com",
        description="Base URL for Jimeng API"
    )
    
    # Default values from the original API
    default_assistant_id: str = Field(
        default="513695",
        description="Default assistant ID"
    )
    
    version_code: str = Field(
        default="5.8.0",
        description="Version code for API requests"
    )
    
    platform_code: str = Field(
        default="7",
        description="Platform code for API requests"
    )
    
    # Request Configuration
    timeout: int = Field(
        default=15,
        description="Request timeout in seconds"
    )
    
    max_retry_count: int = Field(
        default=3,
        description="Maximum retry count for failed requests"
    )
    
    retry_delay: int = Field(
        default=5,
        description="Retry delay in seconds"
    )
    
    # File Configuration
    file_max_size: int = Field(
        default=100 * 1024 * 1024,  # 100MB
        description="Maximum file size in bytes"
    )
    
    # Model Configuration
    default_model: str = Field(
        default="jimeng-3.0",
        description="Default model for image generation"
    )
    
    available_models: List[str] = Field(
        default=[
            "jimeng-3.0",
            "jimeng-2.1", 
            "jimeng-2.0-pro",
            "jimeng-2.0",
            "jimeng-1.4",
            "jimeng-xl-pro"
        ],
        description="Available models for image generation"
    )
    
    # Default image generation parameters
    default_width: int = Field(default=1024, description="Default image width")
    default_height: int = Field(default=1024, description="Default image height")
    default_sample_strength: float = Field(default=0.5, description="Default sample strength")
    
    @classmethod
    def from_env(cls) -> "JimengConfig":
        """Create configuration from environment variables"""
        return cls(
            base_url=os.getenv("JIMENG_BASE_URL", "https://jimeng.jianying.com"),
            timeout=int(os.getenv("JIMENG_TIMEOUT", "15")),
            max_retry_count=int(os.getenv("JIMENG_MAX_RETRY_COUNT", "3")),
            retry_delay=int(os.getenv("JIMENG_RETRY_DELAY", "5")),
            default_model=os.getenv("JIMENG_DEFAULT_MODEL", "jimeng-3.0"),
        )


# Global configuration instance
config = JimengConfig.from_env()
