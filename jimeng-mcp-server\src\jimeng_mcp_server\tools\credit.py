"""
Credit management tool for Jimeng MCP Server
"""

from typing import Any, Dict, Optional
from pydantic import BaseModel

from ..core.client import JimengClient, CreditInfo


class CreditTool:
    """MCP tool for credit management"""
    
    def __init__(self, client: JimengClient):
        """
        Initialize credit tool
        
        Args:
            client: Jimeng API client
        """
        self.client = client
    
    async def get_credit_info(self, session_id: Optional[str] = None) -> CreditInfo:
        """
        Get credit information
        
        Args:
            session_id: Session ID for authentication
        
        Returns:
            Credit information
        """
        return await self.client.get_credit(session_id)
    
    async def receive_daily_credit(self, session_id: Optional[str] = None) -> int:
        """
        Receive daily credit
        
        Args:
            session_id: Session ID for authentication
        
        Returns:
            Current total credits after receiving
        """
        return await self.client.receive_credit(session_id)
    
    async def check_token_status(self, session_id: str) -> bool:
        """
        Check token status
        
        Args:
            session_id: Session ID to check
        
        Returns:
            True if token is valid, False otherwise
        """
        return await self.client.check_token_status(session_id)
    
    @staticmethod
    def get_credit_info_tool_definition() -> Dict[str, Any]:
        """Get MCP tool definition for getting credit info"""
        return {
            "name": "jimeng_get_credit",
            "description": "Get current credit information including gift, purchase, and VIP credits.",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "session_id": {
                        "type": "string",
                        "description": "Session ID for authentication (optional if tokens are configured)"
                    }
                },
                "required": []
            }
        }
    
    @staticmethod
    def get_receive_credit_tool_definition() -> Dict[str, Any]:
        """Get MCP tool definition for receiving daily credit"""
        return {
            "name": "jimeng_receive_credit",
            "description": "Receive daily credit allowance. Usually provides 66 credits per day.",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "session_id": {
                        "type": "string",
                        "description": "Session ID for authentication (optional if tokens are configured)"
                    }
                },
                "required": []
            }
        }
    
    @staticmethod
    def get_token_status_tool_definition() -> Dict[str, Any]:
        """Get MCP tool definition for checking token status"""
        return {
            "name": "jimeng_check_token",
            "description": "Check if a session token is valid and active.",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "session_id": {
                        "type": "string",
                        "description": "Session ID to check"
                    }
                },
                "required": ["session_id"]
            }
        }
    
    async def execute_get_credit(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute get credit info tool
        
        Args:
            arguments: Tool arguments
        
        Returns:
            Tool execution result
        """
        try:
            session_id = arguments.get("session_id")
            credit_info = await self.get_credit_info(session_id)
            
            return {
                "gift_credit": credit_info.gift_credit,
                "purchase_credit": credit_info.purchase_credit,
                "vip_credit": credit_info.vip_credit,
                "total_credit": credit_info.total_credit,
                "message": f"Total credits: {credit_info.total_credit} (Gift: {credit_info.gift_credit}, Purchase: {credit_info.purchase_credit}, VIP: {credit_info.vip_credit})"
            }
            
        except Exception as e:
            return {
                "error": str(e),
                "error_type": type(e).__name__
            }
    
    async def execute_receive_credit(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute receive daily credit tool
        
        Args:
            arguments: Tool arguments
        
        Returns:
            Tool execution result
        """
        try:
            session_id = arguments.get("session_id")
            total_credits = await self.receive_daily_credit(session_id)
            
            return {
                "total_credits": total_credits,
                "message": f"Daily credit received successfully. Current total credits: {total_credits}"
            }
            
        except Exception as e:
            return {
                "error": str(e),
                "error_type": type(e).__name__
            }
    
    async def execute_check_token(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute check token status tool
        
        Args:
            arguments: Tool arguments
        
        Returns:
            Tool execution result
        """
        try:
            session_id = arguments.get("session_id")
            if not session_id:
                return {
                    "error": "session_id is required",
                    "error_type": "MissingParameter"
                }
            
            is_valid = await self.check_token_status(session_id)
            
            return {
                "session_id": session_id,
                "is_valid": is_valid,
                "message": f"Token is {'valid' if is_valid else 'invalid'}"
            }
            
        except Exception as e:
            return {
                "error": str(e),
                "error_type": type(e).__name__
            }
